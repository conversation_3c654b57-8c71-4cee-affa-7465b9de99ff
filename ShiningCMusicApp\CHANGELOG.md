# Changelog

All notable changes to the Shining C Music App will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Changed
- **Recurrence Editor Modernization** - Replaced custom recurrence UI with Syncfusion SfRecurrenceEditor
  - Removed 200+ lines of custom recurrence code and replaced with enterprise-grade component
  - Eliminated custom HTML controls (checkboxes, dropdowns, inputs) for recurrence configuration
  - Automatic iCalendar RFC 5545 compliant RRULE generation
  - Built-in validation and error handling
  - Professional UI with consistent Syncfusion styling
  - Improved maintainability and reduced technical debt

- **Event Editor Time Picker Enhancement** - Upgraded to 15-minute interval time selection
  - Replaced HTML datetime-local inputs with Syncfusion SfDateTimePicker components
  - 15-minute step intervals for better lesson scheduling precision
  - Consistent UI styling with other Syncfusion components
  - Better user experience with dropdown time selection

- **Single Occurrence Editing UX** - Enhanced recurring event editing workflow
  - Recurrence section automatically hidden when editing single occurrences
  - Clear informational message explaining single occurrence vs. series editing
  - Prevents user confusion about recurrence modification scope
  - Maintains proper Syncfusion recurring event editing behavior

- **Environment Variable Naming** - Improved environment variable naming convention
  - Changed `ConnectionStrings_MusicSchool` to `DATABASE_CONNECTION_STRING`
  - More descriptive and follows standard naming conventions
  - Updated all service implementations, deployment scripts, and documentation
  - **BREAKING CHANGE**: Requires updating environment variables in deployment environments

- **Session Timeout Configuration Optimization** - Improved SessionTimeoutService initialization
  - Eliminated duplicate HTTP calls for configuration retrieval
  - Configuration now loaded once in Program.cs and passed to service constructor
  - Improved application startup performance
  - Reduced potential network failure points during service initialization
  - Updated documentation to reflect architectural improvements

### Added
- **Recurring Events Implementation** - Complete recurring lesson scheduling system
  - Support for Daily, Weekly, and Monthly recurrence patterns
  - Configurable intervals (every N days/weeks/months)
  - Customizable end conditions (number of occurrences)
  - Weekly pattern with specific day selection
  - Real-time preview of recurrence schedule
  - RRULE standard compliance for calendar integration
  - Comprehensive validation and error handling

- **Recurrence Section Focus** - Automatic smooth scroll to recurrence options when enabled
  - Improves form usability by drawing attention to newly visible options
  - Smooth animation using native `scrollIntoView` API
  - Works across all devices and browsers
  - Enhanced user experience for lesson scheduling workflow

- **Session Timeout Feature** - Automatic user logout after configurable period of inactivity
  - Default timeout: 30 minutes
  - Configurable via environment variable `SESSION_TIMEOUT_MINUTES` or appsettings.json
  - User activity detection (mouse, keyboard, scroll, touch events)
  - Automatic logout and redirect to login page
  - Performance optimized with throttled activity reporting
  - Comprehensive logging for debugging and monitoring

### Security
- Enhanced session security with automatic timeout functionality
- Prevents unauthorized access to unattended sessions
- Configurable timeout duration for different security requirements

### Technical Details
- **Recurrence Editor Modernization**:
  - Added `@using Syncfusion.Blazor.Calendars` for SfRecurrenceEditor
  - Replaced custom recurrence variables with Syncfusion configuration properties
  - Removed 10+ custom recurrence methods (BuildRecurrenceRule, ParseRecurrenceRule, etc.)
  - Automatic two-way binding with `scheduleEvent.RecurrenceRule`
  - Conditional rendering based on `CurrentAction.EditOccurrence`

- **Time Picker Enhancement**:
  - Upgraded from HTML `datetime-local` to `SfDateTimePicker`
  - Added `Step="15"` property for 15-minute intervals
  - Consistent Bootstrap styling with `CssClass="form-control"`
  - Better date/time format control with `Format="dd/MM/yyyy HH:mm"`

- **Session Management**:
  - New `SessionTimeoutService` for timeout management
  - JavaScript integration for user activity tracking
  - Integration with existing authentication system
  - Environment variable support for deployment flexibility
  - Comprehensive documentation and testing guidelines

### Files Added
- `Services/SessionTimeoutService.cs` - Core session timeout logic
- `Components/SessionTimeoutInitializer.razor` - Service initialization component
- `Documentation/SessionTimeout_Implementation.md` - Complete implementation documentation
- `Documentation/SessionTimeout_QuickReference.md` - Quick reference guide
- `CHANGELOG.md` - This changelog file

### Files Modified
- `ShiningCMusicApp/Pages/Lessons.razor` - Major recurrence editor modernization
  - Replaced custom recurrence UI with SfRecurrenceEditor component
  - Updated time inputs to use SfDateTimePicker with 15-minute intervals
  - Added conditional rendering for single occurrence editing
  - Removed 200+ lines of custom recurrence code
  - Added CSS styling for recurrence editor
- `ShiningCMusicCommon/Models/AppConfiguration.cs` - Added SessionTimeoutMinutes property
- `ShiningCMusicApi/Controllers/ConfigurationController.cs` - Added timeout configuration endpoint
- `ShiningCMusicApi/appsettings.json` - Added SessionTimeout configuration section
- `ShiningCMusicApp/wwwroot/appsettings.json` - Added SessionTimeout configuration section
- `ShiningCMusicApp/wwwroot/js/app.js` - Added user activity tracking functionality
- `ShiningCMusicApp/Services/CustomAuthenticationStateProvider.cs` - Integrated session timeout
- `ShiningCMusicApp/Program.cs` - Registered session timeout service
- `ShiningCMusicApp/Layout/MainLayout.razor` - Added session timeout initializer

### Configuration
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

Environment Variable:
```bash
SESSION_TIMEOUT_MINUTES=30
```

---

## Previous Changes

*Note: This changelog was created with the session timeout implementation. Previous changes were not documented in this format.*
