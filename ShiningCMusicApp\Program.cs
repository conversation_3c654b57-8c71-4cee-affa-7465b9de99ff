using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using Syncfusion.Blazor;
using System.Net.Http.Json;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Load configuration from API server endpoint (which can read environment variables)
var http = new HttpClient();
string apiBaseUrl;
int sessionTimeoutMinutes = 30; // Default value

// Determine the API server URL dynamically
string configurationEndpoint;

var localHttp = new HttpClient() { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
var localConfig = await localHttp.GetFromJsonAsync<Dictionary<string, string>>("appsettings.json");

if (builder.HostEnvironment.BaseAddress.Contains("azurewebsites.net"))
{
    // In Azure, use the same domain as the Blazor app for the API
    var baseUri = new Uri(builder.HostEnvironment.BaseAddress);
    apiBaseUrl = $"{baseUri.Scheme}://{baseUri.Host}/api";
}
else
{
    // Local development - read from appsettings.json
    try
    {
        if (localConfig != null && localConfig.TryGetValue("ApiBaseUrl", out var localApiUrl))
        {
            apiBaseUrl = localApiUrl;
        }
        else
        {
            apiBaseUrl = "https://localhost:7268/api"; // fallback
        }
    }
    catch
    {
        apiBaseUrl = "https://localhost:7268/api"; // fallback
    }
}
configurationEndpoint = $"{apiBaseUrl}/configuration";

Console.WriteLine($"Attempting to load configuration from: {configurationEndpoint}");
try
{
    // Try to get configuration from API server endpoint first
    var serverConfig = await http.GetFromJsonAsync<ShiningCMusicCommon.Models.AppConfiguration>(configurationEndpoint);

    if (serverConfig != null && !string.IsNullOrEmpty(serverConfig.ApiBaseUrl))
    {
        apiBaseUrl = serverConfig.ApiBaseUrl;
        sessionTimeoutMinutes = serverConfig.SessionTimeoutMinutes;

        // Register Syncfusion license from server configuration
        if (!string.IsNullOrEmpty(serverConfig.SyncfusionLicense))
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(serverConfig.SyncfusionLicense);
        }

        Console.WriteLine($"Loaded configuration from server: {apiBaseUrl}, Session timeout: {sessionTimeoutMinutes} minutes");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Failed to load server configuration: {ex.Message}");
    if (localConfig != null)
    {
        apiBaseUrl = localConfig.GetValueOrDefault("ApiBaseUrl", "https://localhost:7268/api");
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(localConfig.GetValueOrDefault("SyncfusionLicense"));
        sessionTimeoutMinutes = int.TryParse(localConfig.GetValueOrDefault("SessionTimeoutMinutes"), out int val) ? val : 30;
    }
}

builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Register API base URL as a named service
builder.Services.AddScoped(provider => new ApiConfiguration { BaseUrl = apiBaseUrl });

// Add authentication services
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<CustomAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());

// Add session timeout service with configured timeout minutes
builder.Services.AddScoped<ISessionTimeoutService>(provider =>
    new SessionTimeoutService(
        provider.GetRequiredService<IJSRuntime>(),
        provider.GetRequiredService<CustomAuthenticationStateProvider>(),
        provider.GetRequiredService<NavigationManager>(),
        sessionTimeoutMinutes));

// Add our services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<IDialogService, DialogService>();

// Add individual API services
builder.Services.AddScoped<ILessonApiService, LessonApiService>();
builder.Services.AddScoped<ITutorApiService, TutorApiService>();
builder.Services.AddScoped<IStudentApiService, StudentApiService>();
builder.Services.AddScoped<ISubjectApiService, SubjectApiService>();
builder.Services.AddScoped<ILocationApiService, LocationApiService>();
builder.Services.AddScoped<IUserApiService, UserApiService>();

// Add Syncfusion Blazor service
builder.Services.AddSyncfusionBlazor();

await builder.Build().RunAsync();
